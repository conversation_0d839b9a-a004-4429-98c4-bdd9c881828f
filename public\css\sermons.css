/* أنماط صفحة الخطب الجاهزة */

/* رأس الصفحة */
.page-header {
    background: linear-gradient(rgba(26, 58, 74, 0.8), rgba(26, 58, 74, 0.8)), url('../images/mosque-interior.jpg');
    background-size: cover;
    background-position: center;
    color: var(--text-light);
    padding: 60px 0;
    text-align: center;
    margin-bottom: 40px;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--secondary-color);
}

/* قسم البحث والتصفية */
.search-filter-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--bg-light);
    border-radius: 8px;
}

.search-box {
    display: flex;
    flex: 1;
    max-width: 500px;
    margin-left: 20px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px 0 0 5px;
    font-size: 1rem;
}

.search-box button {
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    padding: 0 20px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
}

.filter-group label {
    margin-left: 10px;
    font-weight: bold;
}

.filter-group select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--light-color);
}

/* قسم الخطب */
.sermons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.sermon-card {
    background-color: var(--light-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid var(--border-color);
}

.sermon-card:hover {
    transform: translateY(-5px);
}

.sermon-card.featured {
    border: 2px solid var(--secondary-color);
}

.sermon-header {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    background-color: var(--bg-light);
}

.sermon-category {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.featured-badge {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.sermon-title {
    padding: 15px 15px 10px;
    font-size: 1.3rem;
    color: var(--dark-color);
}

.sermon-meta {
    padding: 0 15px 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.9rem;
    color: #777;
}

.sermon-excerpt {
    padding: 0 15px 15px;
    flex-grow: 1;
}

.sermon-footer {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
}

.sermon-stats {
    display: flex;
    gap: 15px;
    color: #777;
    font-size: 0.9rem;
}

/* تصنيفات الخطب */
.sermon-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
    justify-content: center;
}

.category-btn {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-btn:hover, .category-btn.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

/* أنماط زر الحذف */
.sermon-actions {
    display: flex;
    gap: 10px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.9rem;
}

/* أنماط الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    min-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification.success {
    border-right: 4px solid var(--primary-color);
}

.notification.success i {
    color: var(--primary-color);
}

.notification.info {
    border-right: 4px solid #17a2b8;
}

.notification.info i {
    color: #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #777;
}

.notification-close:hover {
    color: #333;
}