// ملف JavaScript لصفحة إعداد الخطبة

document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة إعداد الخطبة');

    // تهيئة المتغيرات
    const form = document.getElementById('prepare-sermon-form');
    const aiSuggestionModal = document.getElementById('ai-suggestion-modal');
    const aiResults = document.getElementById('ai-results');
    const aiLoading = document.getElementById('ai-loading');
    const aiPromptInput = document.getElementById('ai-prompt');
    const generateAiBtn = document.getElementById('generate-ai-btn');
    const useSuggestionBtn = document.getElementById('use-suggestion-btn');
    const closeModalBtns = document.querySelectorAll('.close-modal-btn');
    const exportBtn = document.getElementById('export-btn');
    const exportOptions = document.querySelector('.export-options');
    const saveBtn = document.getElementById('save-draft-btn');
    const printBtn = document.getElementById('print-sermon');
    const clearBtn = document.getElementById('clear-btn');
    const previewBtn = document.getElementById('preview-btn');

    let currentTarget = null; // لتخزين الحقل المستهدف للاقتراحات

    // حفظ البيانات في التخزين المحلي
    function saveToLocalStorage() {
        const formData = {};
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            formData[input.id] = input.value;
        });

        localStorage.setItem('sermon-draft', JSON.stringify(formData));
        showNotification('تم حفظ المسودة بنجاح');
    }

    // استرجاع البيانات من التخزين المحلي
    function loadFromLocalStorage() {
        const savedData = localStorage.getItem('sermon-draft');

        if (savedData) {
            const formData = JSON.parse(savedData);
            const inputs = form.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                if (formData[input.id]) {
                    input.value = formData[input.id];
                }
            });

            showNotification('تم استرجاع المسودة المحفوظة');
        }
    }

    // عرض إشعار للمستخدم
    function showNotification(message, type = 'success') {
        // إزالة أي إشعارات سابقة
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // إضافة مستمع حدث لزر الإغلاق
        const closeButton = notification.querySelector('.notification-close');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            });
        }

        // تأخير قصير قبل إظهار الإشعار للسماح بتطبيق التنسيق
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // إخفاء الإشعار تلقائيًا بعد 3 ثوان
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // تنفيذ الحفظ التلقائي كل دقيقة
    setInterval(saveToLocalStorage, 60000);

    // تحميل البيانات المحفوظة عند تحميل الصفحة
    loadFromLocalStorage();

    // معالجة أزرار النسخ
    const copyBtns = document.querySelectorAll('.copy-btn');
    copyBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.select();
                document.execCommand('copy');
                showNotification('تم نسخ النص');
            }
        });
    });

    // معالجة أزرار الاقتراح
    const suggestBtns = document.querySelectorAll('.ai-suggest-btn');
    suggestBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            currentTarget = document.getElementById(targetId);

            if (currentTarget) {
                aiSuggestionModal.style.display = 'flex';
                aiResults.innerHTML = '';
                aiLoading.style.display = 'none';

                // تعبئة حقل الاقتراح بناءً على عنوان الخطبة أو الموضوع إذا كان متاحًا
                const sermonTitle = document.getElementById('sermon-title').value;
                const sermonTopic = document.getElementById('sermon-topic').value;

                if (sermonTitle || sermonTopic) {
                    aiPromptInput.value = sermonTitle || sermonTopic;
                } else {
                    aiPromptInput.value = '';
                }
            }
        });
    });

    // إغلاق النافذة المنبثقة
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            aiSuggestionModal.style.display = 'none';
        });
    });

    // معالجة زر توليد الاقتراحات
    if (generateAiBtn) {
        generateAiBtn.addEventListener('click', function() {
            const prompt = aiPromptInput.value.trim();

            if (prompt) {
                aiLoading.style.display = 'flex';
                aiResults.innerHTML = '';

                // محاكاة استجابة الذكاء الاصطناعي (في التطبيق الحقيقي، سيتم استبدال هذا بطلب API)
                setTimeout(() => {
                    generateMockAiResponse(prompt, currentTarget.id);
                    aiLoading.style.display = 'none';
                }, 1500);
            } else {
                showNotification('يرجى إدخال موضوع أو كلمات مفتاحية');
            }
        });
    }

    // استخدام الاقتراح المحدد
    if (useSuggestionBtn) {
        useSuggestionBtn.addEventListener('click', function() {
            const selectedSuggestion = aiResults.querySelector('.suggestion.selected');

            if (selectedSuggestion && currentTarget) {
                currentTarget.value = selectedSuggestion.textContent;
                aiSuggestionModal.style.display = 'none';
                showNotification('تم استخدام الاقتراح بنجاح');
            } else {
                showNotification('يرجى تحديد اقتراح أولاً');
            }
        });
    }

    // توليد استجابة وهمية للذكاء الاصطناعي (للعرض التوضيحي فقط)
    function generateMockAiResponse(prompt, targetId) {
        let suggestions = [];

        // اقتراحات مختلفة بناءً على نوع الحقل
        switch (targetId) {
            case 'sermon-title':
                suggestions = [
                    `${prompt} - دروس وعبر`,
                    `${prompt} في ضوء القرآن والسنة`,
                    `أهمية ${prompt} في حياة المسلم`,
                    `فضائل ${prompt} وآثارها على الفرد والمجتمع`
                ];
                break;

            case 'intro-tahmid':
                suggestions = [
                    'إن الحمد لله نحمده ونستعينه ونستغفره، ونعوذ بالله من شرور أنفسنا ومن سيئات أعمالنا، من يهده الله فلا مضل له، ومن يضلل فلا هادي له، وأشهد أن لا إله إلا الله وحده لا شريك له، وأشهد أن محمداً عبده ورسوله.',
                    'الحمد لله رب العالمين، الرحمن الرحيم، مالك يوم الدين، والصلاة والسلام على أشرف الأنبياء والمرسلين، نبينا محمد وعلى آله وصحبه أجمعين.',
                    'الحمد لله الذي خلق فسوى، وقدر فهدى، وأغنى وأقنى، وجعل لنا من أنفسنا أزواجاً، وجعل لنا من الأزواج بنين وحفدة، ورزقنا من الطيبات، فله الحمد في الأولى والآخرة.'
                ];
                break;

            case 'intro-salah':
                suggestions = [
                    'وأصلي وأسلم على نبينا محمد، خير البرية، وأفضل من وطئ الثرى، صلى الله عليه وعلى آله وصحبه وسلم تسليماً كثيراً.',
                    'والصلاة والسلام على المبعوث رحمة للعالمين، سيدنا محمد وعلى آله وصحبه أجمعين.',
                    'وأصلي وأسلم على خاتم الأنبياء والمرسلين، نبينا محمد، الذي بلغ الرسالة، وأدى الأمانة، ونصح الأمة، وجاهد في الله حق جهاده.'
                ];
                break;

            default:
                // اقتراحات عامة للمحتوى
                suggestions = [
                    `محتوى مقترح حول ${prompt} - الاقتراح الأول`,
                    `محتوى مقترح حول ${prompt} - الاقتراح الثاني`,
                    `محتوى مقترح حول ${prompt} - الاقتراح الثالث`
                ];
        }

        // عرض الاقتراحات
        suggestions.forEach(suggestion => {
            const suggestionElement = document.createElement('div');
            suggestionElement.className = 'suggestion';
            suggestionElement.textContent = suggestion;

            suggestionElement.addEventListener('click', function() {
                // إزالة التحديد من جميع الاقتراحات
                document.querySelectorAll('.suggestion').forEach(el => {
                    el.classList.remove('selected');
                });

                // تحديد الاقتراح الحالي
                this.classList.add('selected');
            });

            aiResults.appendChild(suggestionElement);
        });

        // تحديد الاقتراح الأول تلقائياً
        const firstSuggestion = aiResults.querySelector('.suggestion');
        if (firstSuggestion) {
            firstSuggestion.classList.add('selected');
        }
    }

    // معالجة زر التصدير وخياراته
    if (exportBtn) {
        exportBtn.addEventListener('click', function(event) {
            event.stopPropagation(); // منع انتشار الحدث
            exportOptions.classList.toggle('show');
        });

        // إخفاء خيارات التصدير عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('#export-btn') && !event.target.closest('.export-options')) {
                exportOptions.classList.remove('show');
            }
        });

        // منع إغلاق القائمة عند النقر داخلها
        exportOptions.addEventListener('click', function(event) {
            event.stopPropagation(); // منع انتشار الحدث
        });
    }

    // معالجة أزرار التصدير
    const exportDocxBtn = document.getElementById('export-word');
    const printSermonBtn = document.getElementById('print-sermon');

    if (exportDocxBtn) {
        exportDocxBtn.addEventListener('click', function() {
            exportToDocx();
        });
    }

    if (printSermonBtn) {
        printSermonBtn.addEventListener('click', function() {
            window.print();
        });
    }

    // تصدير إلى ملف Word
    function exportToDocx() {
        try {
            // الوصول إلى مكتبة docx من النطاق العالمي
            const docx = window.docx;

            if (!docx) {
                showNotification('لم يتم تحميل مكتبة docx بشكل صحيح', 'error');
                return;
            }

            const { Document, Packer, Paragraph, HeadingLevel, AlignmentType } = docx;

            const title = document.getElementById('sermon-title').value || 'خطبة جديدة';
            const topic = document.getElementById('sermon-topic').value || '';
            const preacherName = document.getElementById('preacher-name').value || 'غير محدد';

            // إنشاء فقرات المستند
            const paragraphs = [];

            // إضافة العنوان
            paragraphs.push(
                new Paragraph({
                    text: title,
                    heading: HeadingLevel.HEADING_1,
                    alignment: AlignmentType.RIGHT
                })
            );

            // إضافة الموضوع إذا كان موجوداً
            if (topic) {
                paragraphs.push(
                    new Paragraph({
                        text: topic,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة اسم الخطيب
            paragraphs.push(
                new Paragraph({
                    text: `إعداد: ${preacherName}`,
                    alignment: AlignmentType.RIGHT
                })
            );

            // إضافة فقرة فارغة
            paragraphs.push(new Paragraph({}));

            // إضافة مقدمة الخطبة الأولى
            paragraphs.push(
                new Paragraph({
                    text: 'مقدمة الخطبة الأولى',
                    heading: HeadingLevel.HEADING_2,
                    alignment: AlignmentType.RIGHT
                })
            );

            // إضافة التحميد
            const tahmid = document.getElementById('intro-tahmid').value;
            if (tahmid) {
                paragraphs.push(
                    new Paragraph({
                        text: tahmid,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة الصلاة على النبي
            const salah = document.getElementById('intro-salah').value;
            if (salah) {
                paragraphs.push(
                    new Paragraph({
                        text: salah,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة الشعر والنثر
            const poetry = document.getElementById('intro-poetry').value;
            if (poetry) {
                paragraphs.push(
                    new Paragraph({
                        text: poetry,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة فقرة فارغة
            paragraphs.push(new Paragraph({}));

            // إضافة الخطبة الأولى
            paragraphs.push(
                new Paragraph({
                    text: 'الخطبة الأولى',
                    heading: HeadingLevel.HEADING_2,
                    alignment: AlignmentType.RIGHT
                })
            );

            // إضافة محتوى الخطبة الأولى
            const firstSermonContent = document.getElementById('first-sermon-content').value;
            if (firstSermonContent) {
                paragraphs.push(
                    new Paragraph({
                        text: firstSermonContent,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة خاتمة الخطبة الأولى
            const firstSermonConclusion = document.getElementById('first-sermon-conclusion').value;
            if (firstSermonConclusion) {
                paragraphs.push(
                    new Paragraph({
                        text: firstSermonConclusion,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة فقرة فارغة
            paragraphs.push(new Paragraph({}));

            // إضافة الخطبة الثانية
            paragraphs.push(
                new Paragraph({
                    text: 'الخطبة الثانية',
                    heading: HeadingLevel.HEADING_2,
                    alignment: AlignmentType.RIGHT
                })
            );

            // إضافة مقدمة الخطبة الثانية
            const secondSermonIntro = document.getElementById('second-sermon-intro').value;
            if (secondSermonIntro) {
                paragraphs.push(
                    new Paragraph({
                        text: secondSermonIntro,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة محتوى الخطبة الثانية
            const secondSermonContent = document.getElementById('second-sermon-content').value;
            if (secondSermonContent) {
                paragraphs.push(
                    new Paragraph({
                        text: secondSermonContent,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة الدعاء
            const dua = document.getElementById('sermon-dua').value;
            if (dua) {
                paragraphs.push(
                    new Paragraph({
                        text: dua,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إضافة خاتمة الخطبة
            const conclusion = document.getElementById('sermon-conclusion').value;
            if (conclusion) {
                paragraphs.push(
                    new Paragraph({
                        text: conclusion,
                        alignment: AlignmentType.RIGHT
                    })
                );
            }

            // إنشاء المستند مع ضبط اتجاه النص من اليمين لليسار
            const doc = new Document({
                sections: [{
                    properties: {
                        page: {
                            margin: {
                                top: 1000,
                                right: 1000,
                                bottom: 1000,
                                left: 1000,
                            },
                        },
                        bidi: true, // تفعيل دعم النص ثنائي الاتجاه (RTL)
                    },
                    children: paragraphs
                }]
            });

            // تصدير المستند إلى ملف
            Packer.toBlob(doc).then(blob => {
                saveAs(blob, `${title}.docx`);
                showNotification('تم تصدير الخطبة بنجاح إلى ملف Word');
            }).catch(error => {
                console.error('خطأ في تصدير المستند:', error);
                showNotification('حدث خطأ أثناء تصدير المستند', 'error');
            });
        } catch (error) {
            console.error('خطأ في تصدير المستند:', error);
            showNotification('حدث خطأ أثناء تصدير المستند', 'error');
        }
    }

    // تم إزالة وظيفة تصدير PDF

    // تصدير إلى ملف نصي
    function exportToText() {
        const title = document.getElementById('sermon-title').value || 'خطبة جديدة';
        const preacherName = document.getElementById('preacher-name').value || 'غير محدد';

        let content = `${title}\n`;
        content += `إعداد: ${preacherName}\n\n`;

        content += `مقدمة الخطبة الأولى:\n`;
        content += `${document.getElementById('intro-tahmid').value || ''}\n`;
        content += `${document.getElementById('intro-salah').value || ''}\n\n`;

        content += `الخطبة الأولى:\n`;
        content += `${document.getElementById('first-sermon-content').value || ''}\n`;
        content += `${document.getElementById('first-sermon-conclusion').value || ''}\n\n`;

        content += `الخطبة الثانية:\n`;
        content += `${document.getElementById('second-sermon-intro').value || ''}\n`;
        content += `${document.getElementById('second-sermon-content').value || ''}\n`;
        content += `${document.getElementById('sermon-dua').value || ''}\n`;
        content += `${document.getElementById('sermon-conclusion').value || ''}\n`;

        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        saveAs(blob, `${title}.txt`);
        showNotification('تم تصدير الخطبة بنجاح إلى ملف نصي');
    }

    // معالجة زر الحفظ
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            saveToLocalStorage();
        });
    }

    // تم نقل معالجة زر الطباعة إلى الأعلى

    // معالجة زر المسح
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من رغبتك في مسح جميع البيانات؟')) {
                form.reset();
                localStorage.removeItem('sermon-draft');
                showNotification('تم مسح جميع البيانات', 'error');
            }
        });
    }

    // عداد الأحرف للملخص
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            // حفظ تلقائي عند الكتابة
            saveToLocalStorage();
        });
    });

    // حفظ تلقائي عند تغيير أي حقل
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            saveToLocalStorage();
        });
    });

    // معالجة زر المعاينة
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            previewSermon();
        });
    }

    // وظيفة معاينة الخطبة
    function previewSermon() {
        // إنشاء عنصر المعاينة إذا لم يكن موجوداً
        let previewContainer = document.querySelector('.preview-container');
        if (!previewContainer) {
            previewContainer = document.createElement('div');
            previewContainer.className = 'preview-container';
            form.parentNode.appendChild(previewContainer);
        }

        // الحصول على بيانات الخطبة
        const title = document.getElementById('sermon-title').value || 'خطبة جديدة';
        const topic = document.getElementById('sermon-topic').value || '';
        const preacherName = document.getElementById('preacher-name').value || 'غير محدد';
        const tahmid = document.getElementById('intro-tahmid').value || '';
        const salah = document.getElementById('intro-salah').value || '';
        const poetry = document.getElementById('intro-poetry').value || '';
        const firstSermonContent = document.getElementById('first-sermon-content').value || '';
        const firstSermonConclusion = document.getElementById('first-sermon-conclusion').value || '';
        const secondSermonIntro = document.getElementById('second-sermon-intro').value || '';
        const secondSermonContent = document.getElementById('second-sermon-content').value || '';
        const dua = document.getElementById('sermon-dua').value || '';
        const conclusion = document.getElementById('sermon-conclusion').value || '';

        // إنشاء محتوى المعاينة
        let previewHTML = `
            <div class="preview-header">
                <h2 class="preview-title">${title}</h2>
                ${topic ? `<p class="preview-topic">${topic}</p>` : ''}
                <p class="preview-preacher">إعداد: ${preacherName}</p>
            </div>

            <div class="preview-section">
                <h3 class="preview-section-title">مقدمة الخطبة الأولى</h3>
                ${tahmid ? `<p class="preview-text">${tahmid}</p>` : ''}
                ${salah ? `<p class="preview-text">${salah}</p>` : ''}
                ${poetry ? `<p class="preview-text">${poetry}</p>` : ''}
            </div>

            <div class="preview-section">
                <h3 class="preview-section-title">الخطبة الأولى</h3>
                ${firstSermonContent ? `<p class="preview-text">${firstSermonContent}</p>` : ''}
                ${firstSermonConclusion ? `<p class="preview-text">${firstSermonConclusion}</p>` : ''}
            </div>

            <div class="preview-section">
                <h3 class="preview-section-title">الخطبة الثانية</h3>
                ${secondSermonIntro ? `<p class="preview-text">${secondSermonIntro}</p>` : ''}
                ${secondSermonContent ? `<p class="preview-text">${secondSermonContent}</p>` : ''}
                ${dua ? `<p class="preview-text">${dua}</p>` : ''}
                ${conclusion ? `<p class="preview-text">${conclusion}</p>` : ''}
            </div>

            <div class="preview-actions">
                <button type="button" class="btn btn-secondary close-preview-btn">
                    <i class="fas fa-times"></i> إغلاق المعاينة
                </button>
            </div>
        `;

        // عرض المعاينة
        previewContainer.innerHTML = previewHTML;
        previewContainer.classList.add('show');

        // التمرير إلى المعاينة
        previewContainer.scrollIntoView({ behavior: 'smooth' });

        // إضافة مستمع حدث لزر الإغلاق
        const closePreviewBtn = previewContainer.querySelector('.close-preview-btn');
        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', function() {
                previewContainer.classList.remove('show');
            });
        }
    }
});

// إضافة CSS للإشعارات
const style = document.createElement('style');
style.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: white;
        color: #333;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        transform: translateX(120%);
        opacity: 1;
        transition: transform 0.3s ease;
        z-index: 1000;
        max-width: 300px;
        width: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification.success {
        border-right: 4px solid #1d8a4e;
    }

    .notification.error {
        border-right: 4px solid #dc3545;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .notification-content i {
        font-size: 1.2rem;
    }

    .notification.success i {
        color: #1d8a4e;
    }

    .notification.error i {
        color: #dc3545;
    }

    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        color: #777;
        font-size: 1rem;
    }

    .ai-suggestion-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .ai-suggestion-content {
        background-color: white;
        border-radius: 10px;
        width: 80%;
        max-width: 600px;
        max-height: 80vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .ai-suggestion-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .ai-suggestion-header h3 {
        margin: 0;
        color: #2c3e50;
    }

    .close-modal-btn {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #7f8c8d;
    }

    .ai-suggestion-body {
        padding: 20px;
        overflow-y: auto;
        flex-grow: 1;
    }

    .ai-input-container {
        display: flex;
        margin-bottom: 20px;
    }

    .ai-input-container input {
        flex-grow: 1;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px 0 0 5px;
    }

    .ai-input-container button {
        border-radius: 0 5px 5px 0;
    }

    .ai-loading {
        display: none;
        flex-direction: column;
        align-items: center;
        margin: 20px 0;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .suggestion {
        padding: 15px;
        border: 1px solid #eee;
        border-radius: 5px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .suggestion:hover {
        background-color: #f9f9f9;
    }

    .suggestion.selected {
        background-color: #e1f0fa;
        border-color: #3498db;
    }

    .ai-suggestion-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .export-options {
        display: none;
        position: absolute;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        z-index: 100;
        margin-top: 10px;
        left: 0;
    }

    .export-option {
        display: block;
        width: 100%;
        text-align: right;
        padding: 10px 15px;
        border: none;
        background: none;
        cursor: pointer;
    }

    .export-option:hover {
        background-color: #f5f5f5;
    }

    /* أنماط المعاينة */
    .preview-container {
        display: none;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 30px;
        margin: 30px 0;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .preview-container.show {
        display: block;
    }

    .preview-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--border-color);
    }

    .preview-title {
        color: var(--primary-color);
        font-size: 1.8rem;
        margin-bottom: 10px;
    }

    .preview-topic {
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .preview-preacher {
        color: #777;
        font-style: italic;
    }

    .preview-section {
        margin-bottom: 30px;
    }

    .preview-section-title {
        color: var(--dark-color);
        font-size: 1.4rem;
        margin-bottom: 15px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
    }

    .preview-text {
        line-height: 1.8;
        margin-bottom: 15px;
        white-space: pre-wrap;
    }

    .preview-actions {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    @media print {
        header, footer, .form-actions, .textarea-actions, .ai-suggest-btn {
            display: none !important;
        }

        body {
            font-size: 12pt;
        }

        .sermon-form-card {
            box-shadow: none;
            border: none;
        }

        textarea, input {
            border: none !important;
            padding: 0 !important;
        }

        .form-group label {
            margin-top: 15px;
        }
    }
`;

document.head.appendChild(style);