/* تجاوب الموقع مع الشاشات المختلفة */
@media (max-width: 992px) {
    .navbar .container {
        flex-direction: column;
    }

    .logo, .slogan {
        margin-bottom: 15px;
    }

    .nav-links {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-section .container {
        flex-direction: column;
    }

    .hero-content, .hero-image {
        width: 100%;
        text-align: center;
        padding: 0;
        margin-bottom: 30px;
    }

    .cta-buttons {
        justify-content: center;
    }

    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: 100%;
        margin-left: 0;
        margin-bottom: 20px;
    }

    .filter-options {
        justify-content: space-between;
    }

    /* تجاوب صفحة العلماء */
    .scholars-container {
        flex-direction: column;
    }

    .sidebar {
        flex: none;
        width: 100%;
        margin-bottom: 30px;
        position: static;
    }
}

@media (max-width: 768px) {
    .features-grid, .sections-grid, .content-grid, .footer-content {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        border-radius: 5px;
        margin-bottom: 10px;
    }

    .newsletter-form button {
        border-radius: 5px;
        width: 100%;
    }

    .sermons-grid {
        grid-template-columns: 1fr;
    }

    .filter-options {
        flex-direction: column;
    }

    .sermon-footer {
        flex-direction: column;
        gap: 15px;
    }

    .sermon-stats {
        width: 100%;
        justify-content: center;
    }

    .sermon-header {
        flex-direction: column;
        gap: 20px;
    }

    .sermon-actions {
        width: 100%;
    }

    .sermon-actions .btn {
        flex: 1;
    }

    .download-menu {
        width: 100%;
        left: 0;
        right: 0;
    }

    .form-card, .auth-card {
        padding: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-actions, .success-actions {
        flex-direction: column;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .social-buttons {
        flex-direction: column;
    }

    .btn-social {
        width: 100%;
    }

    /* تجاوب صفحة العلماء للشاشات الصغيرة */
    .fatwa-header, .fatwa-meta, .fatwa-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .fatwa-actions {
        width: 100%;
        justify-content: space-between;
    }

    .fatwa-stats {
        width: 100%;
        justify-content: center;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .form-actions {
        justify-content: center;
    }

    .form-actions button {
        width: 100%;
    }

    /* تجاوب الصفحة الرئيسية للشاشات الصغيرة */
    .hero-section .container {
        flex-direction: column;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .testimonials-container {
        grid-template-columns: 1fr;
    }

    .content-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 20px;
    }

    .testimonial-card {
        padding: 20px;
    }

    .content-card .content-info {
        padding: 20px;
    }

    .btn-large {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .hero-stats {
        gap: 15px;
    }

    .stat-number {
        font-size: 2rem;
    }
}