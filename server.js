// server.js

const express = require('express');
const path = require('path'); // استيراد وحدة path للتعامل مع المسارات

// إنشاء تطبيق Express
const app = express();

// تحديد المنفذ
const PORT = process.env.PORT || 3000;

// --- Middleware لتقديم الملفات الثابتة ---
// نخبر Express باستخدام المجلد 'public' لتقديم الملفات الثابتة (HTML, CSS, JS, Images)
app.use(express.static(path.join(__dirname, 'public')));

// --- المسارات (Routes) ---

// لم نعد بحاجة لهذا المسار لأن index.html سيُقدم تلقائيًا
// app.get('/', (req, res) => {
//   res.send('مرحباً بك في موقع تمسيمك! (الخادم يعمل)');
// });

// يمكنك إضافة مسارات API هنا لاحقًا (مثل /api/sermons)

// --- تشغيل الخادم ---
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ http://localhost:${PORT}`);
  console.log('يمكنك الآن الوصول للموقع عبر المتصفح.');
});
