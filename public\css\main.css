/* ألوان أساسية متناسقة مع هوية الموقع الإسلامي */
:root {
    --primary-color: #1d8a4e;     /* أخضر إسلامي */
    --secondary-color: #d4af37;   /* ذهبي */
    --dark-color: #1a3a4a;        /* أزرق داكن */
    --light-color: #f8f9fa;       /* أبيض فاتح */
    --accent-color: #c75c5c;      /* أحمر طوبي */
    --text-color: #333;           /* لون النص الأساسي */
    --text-light: #f8f9fa;        /* لون النص الفاتح */
    --border-color: #e0e0e0;      /* لون الحدود */
    --bg-light: #f5f5f5;          /* خلفية فاتحة */
    --bg-dark: #0a2e38;           /* خلفية داكنة */
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-color);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: transparent;
    color: var(--secondary-color);
}