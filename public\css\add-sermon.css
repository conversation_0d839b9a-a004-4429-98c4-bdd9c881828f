/* تنسيقات عامة */
.add-sermon-form-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-card h2 {
    color: #2c3e50;
    font-family: '<PERSON><PERSON>', serif;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

/* تحسينات حقول الإدخال */
.form-group input,
.form-group textarea,
.form-group select {
    border: 1px solid #dcdde1;
    border-radius: 6px;
    padding: 0.8rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* تنسيقات الأزرار */
.form-actions .btn {
    font-family: 'Tajawal', sans-serif;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
}

.btn-outline {
    border: 1px solid #3498db;
    color: #3498db;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .add-sermon-form-container {
        padding: 1rem;
        margin: 1rem;
    }
    
    .form-card h2 {
        font-size: 1.5rem;
    }
}

/* رسائل الإشعارات */
.submission-message {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
}

.success-message i {
    font-size: 3rem;
    color: #27ae60;
    margin-bottom: 1rem;
}

/* تحسينات القوائم المنسدلة */
.select2-container--default .select2-selection--single {
    border-radius: 6px;
    height: 42px;
    padding: 0.5rem;
}

/* تنسيقات العلامات */
.tagify {
    --tag-bg: #3498db;
    --tag-text-color: white;
    --tag-hover: #2980b9;
}

/* ملف CSS لتحسين مظهر صفحة إضافة الخطب */

/* تنسيق عام للصفحة */
body {
    font-family: 'Amiri', 'Scheherazade New', serif !important;
    color: #333 !important;
    background-color: #f5f5f5 !important;
}

/* تنسيق بطاقة النموذج */
.form-card {
    background-color: #fff !important;
    border-radius: 10px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    padding: 30px !important;
    margin-bottom: 40px !important;
}

.form-card h2 {
    color: #2c3e50 !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #3498db !important;
    padding-bottom: 10px !important;
    font-size: 24px !important;
}

.form-card .form-intro {
    color: #666 !important;
    margin-bottom: 25px !important;
    font-size: 16px !important;
}

/* تنسيق مجموعات النموذج */
.form-group {
    margin-bottom: 25px !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 700 !important;
    color: #2c3e50 !important;
    font-size: 16px !important;
}

.form-row {
    display: flex !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
}

.form-row .form-group {
    flex: 1 !important;
    min-width: 250px !important;
}

/* تنسيق حقول الإدخال */
input[type="text"],
input[type="date"],
select,
textarea {
    width: 100% !important;
    padding: 12px 15px !important;
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    font-size: 16px !important;
    font-family: 'Amiri', 'Scheherazade New', serif !important;
    transition: all 0.3s ease !important;
}

input[type="text"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: #3498db !important;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2) !important;
    outline: none !important;
}

textarea {
    min-height: 120px !important;
    resize: vertical !important;
}

textarea#sermon-content {
    min-height: 300px !important;
}

.field-hint {
    font-size: 14px !important;
    color: #777 !important;
    margin-top: 5px !important;
}

/* عداد الأحرف */
.char-count {
    text-align: left !important;
    font-size: 14px !important;
    color: #777 !important;
    margin-top: 5px !important;
}

/* تنسيق خانة الاختيار */
.terms-check {
    display: flex !important;
    align-items: flex-start !important;
    margin-bottom: 20px !important;
}

.terms-check input[type="checkbox"] {
    margin-left: 10px !important;
    margin-top: 5px !important;
}

.terms-check label {
    font-size: 15px !important;
    color: #555 !important;
}

.terms-check a {
    color: #3498db !important;
    text-decoration: none !important;
}

.terms-check a:hover {
    text-decoration: underline !important;
}

/* تنسيق أزرار النموذج */
.form-actions {
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
    margin-top: 30px !important;
}

.btn {
    padding: 12px 25px !important;
    border-radius: 6px !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn-primary {
    background-color: #3498db !important;
    color: white !important;
}

.btn-primary:hover {
    background-color: #2980b9 !important;
    transform: translateY(-2px) !important;
}

.btn-outline {
    background-color: transparent !important;
    color: #3498db !important;
    border: 2px solid #3498db !important;
}

.btn-outline:hover {
    background-color: #f0f8ff !important;
    transform: translateY(-2px) !important;
}

/* تنسيق رسالة النجاح */
.submission-message {
    text-align: center !important;
    padding: 30px !important;
}

.success-message {
    background-color: #f0fff4 !important;
    border: 1px solid #c6f6d5 !important;
    border-radius: 10px !important;
    padding: 30px !important;
}

.success-message i {
    font-size: 60px !important;
    color: #48bb78 !important;
    margin-bottom: 20px !important;
}

.success-message h3 {
    color: #2f855a !important;
    margin-bottom: 15px !important;
    font-size: 24px !important;
}

.success-message p {
    color: #4a5568 !important;
    margin-bottom: 25px !important;
    font-size: 16px !important;
}

.success-actions {
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .form-card {
        padding: 20px !important;
    }
    
    .form-row {
        flex-direction: column !important;
        gap: 0 !important;
    }
    
    .form-actions {
        flex-direction: column !important;
    }
    
    .btn {
        width: 100% !important;
    }
}

/* تأثيرات إضافية */
.form-card {
    animation: fadeIn 0.5s ease-in-out !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين مظهر القائمة المنسدلة */
select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: left 15px center !important;
    background-size: 16px !important;
    padding-left: 40px !important;
}